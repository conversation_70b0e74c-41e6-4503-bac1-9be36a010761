if(!self.define){let e,s={};const a=(a,n)=>(a=new URL(a+".js",n).href,s[a]||new Promise(s=>{if("document"in self){const e=document.createElement("script");e.src=a,e.onload=s,document.head.appendChild(e)}else e=a,importScripts(a),s()}).then(()=>{let e=s[a];if(!e)throw new Error(`Module ${a} didn’t register its module`);return e}));self.define=(n,i)=>{const t=e||("document"in self?document.currentScript.src:"")||location.href;if(s[t])return;let c={};const r=e=>a(e,t),d={module:{uri:t},exports:c,require:r};s[t]=Promise.all(n.map(e=>d[e]||r(e))).then(e=>(i(...e),c))}}define(["./workbox-4754cb34"],function(e){"use strict";importScripts("fallback-KXs0_jI0zVRI97lV7SLzi.js"),self.skipWaiting(),e.clientsClaim(),e.precacheAndRoute([{url:"/_next/app-build-manifest.json",revision:"5c345d4feaef59d8982bdf048de525c5"},{url:"/_next/static/KXs0_jI0zVRI97lV7SLzi/_buildManifest.js",revision:"2bce04d8e576573ca4bc65dd9fe9f310"},{url:"/_next/static/KXs0_jI0zVRI97lV7SLzi/_ssgManifest.js",revision:"b6652df95db52feb4daf4eca35380933"},{url:"/_next/static/chunks/195-2306081a40b370bc.js",revision:"KXs0_jI0zVRI97lV7SLzi"},{url:"/_next/static/chunks/463-869bb23a47df17c9.js",revision:"KXs0_jI0zVRI97lV7SLzi"},{url:"/_next/static/chunks/4bd1b696-a304e74c827e0bf2.js",revision:"KXs0_jI0zVRI97lV7SLzi"},{url:"/_next/static/chunks/6-f7b70d4293ae7238.js",revision:"KXs0_jI0zVRI97lV7SLzi"},{url:"/_next/static/chunks/684-fdb8f1d7f2dd4598.js",revision:"KXs0_jI0zVRI97lV7SLzi"},{url:"/_next/static/chunks/app/%5Blocale%5D/about/page-7c0856901cdc9207.js",revision:"KXs0_jI0zVRI97lV7SLzi"},{url:"/_next/static/chunks/app/%5Blocale%5D/dashboard/page-f183b070164d0f11.js",revision:"KXs0_jI0zVRI97lV7SLzi"},{url:"/_next/static/chunks/app/%5Blocale%5D/donate/page-656c966588e8589d.js",revision:"KXs0_jI0zVRI97lV7SLzi"},{url:"/_next/static/chunks/app/%5Blocale%5D/layout-d8cbf66884ce6127.js",revision:"KXs0_jI0zVRI97lV7SLzi"},{url:"/_next/static/chunks/app/%5Blocale%5D/page-abaa9221361af4f4.js",revision:"KXs0_jI0zVRI97lV7SLzi"},{url:"/_next/static/chunks/app/_not-found/page-952bd0b91dca6b7d.js",revision:"KXs0_jI0zVRI97lV7SLzi"},{url:"/_next/static/chunks/framework-f593a28cde54158e.js",revision:"KXs0_jI0zVRI97lV7SLzi"},{url:"/_next/static/chunks/main-2b8f82b9560ae2f7.js",revision:"KXs0_jI0zVRI97lV7SLzi"},{url:"/_next/static/chunks/main-app-d0a7377698c551b6.js",revision:"KXs0_jI0zVRI97lV7SLzi"},{url:"/_next/static/chunks/pages/_app-da15c11dea942c36.js",revision:"KXs0_jI0zVRI97lV7SLzi"},{url:"/_next/static/chunks/pages/_error-cc3f077a18ea1793.js",revision:"KXs0_jI0zVRI97lV7SLzi"},{url:"/_next/static/chunks/polyfills-42372ed130431b0a.js",revision:"846118c33b2c0e922d7b3a7676f81f6f"},{url:"/_next/static/chunks/webpack-b4e7b61d68d47783.js",revision:"KXs0_jI0zVRI97lV7SLzi"},{url:"/_next/static/css/ddb9fee86b1a51f0.css",revision:"ddb9fee86b1a51f0"},{url:"/_next/static/media/26a46d62cd723877-s.woff2",revision:"befd9c0fdfa3d8a645d5f95717ed6420"},{url:"/_next/static/media/55c55f0601d81cf3-s.woff2",revision:"43828e14271c77b87e3ed582dbff9f74"},{url:"/_next/static/media/581909926a08bbc8-s.woff2",revision:"f0b86e7c24f455280b8df606b89af891"},{url:"/_next/static/media/75fbaf8367ea912d-s.p.woff2",revision:"7753f89d68285d56289210e1a7dc71aa"},{url:"/_next/static/media/8e9860b6e62d6359-s.woff2",revision:"01ba6c2a184b8cba08b0d57167664d75"},{url:"/_next/static/media/97e0cb1ae144a2a9-s.woff2",revision:"e360c61c5bd8d90639fd4503c829c2dc"},{url:"/_next/static/media/b4a68bcb11b9f442-s.woff2",revision:"1db89f1d3156df92e52ad657f17624b6"},{url:"/_next/static/media/d216a8747c1e09af-s.woff2",revision:"c0ca3d5fb604bca2f5cd4759f42c8852"},{url:"/_next/static/media/df0a9ae256c0569c-s.woff2",revision:"d54db44de5ccb18886ece2fda72bdfe0"},{url:"/_next/static/media/e4af272ccee01ff0-s.p.woff2",revision:"65850a373e258f1c897a2b3d75eb74de"},{url:"/icons/icon-192.png",revision:"5ea99f02b68894ca280598b02b88d445"},{url:"/icons/icon-512.png",revision:"2145fbf59a8a80c80193562d0b5cb8aa"},{url:"/manifest.json",revision:"5d1aa8a9a66f87141f15705dc7d5112d"},{url:"/offline.html",revision:"5ec2885a1903ed5411e209f299d5287c"}],{ignoreURLParametersMatching:[]}),e.cleanupOutdatedCaches(),e.registerRoute("/",new e.NetworkFirst({cacheName:"start-url",plugins:[{cacheWillUpdate:async({request:e,response:s,event:a,state:n})=>s&&"opaqueredirect"===s.type?new Response(s.body,{status:200,statusText:"OK",headers:s.headers}):s},{handlerDidError:async({request:e})=>self.fallback(e)}]}),"GET"),e.registerRoute(/^https:\/\/fonts\.(?:gstatic)\.com\/.*/i,new e.CacheFirst({cacheName:"google-fonts-webfonts",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:31536e3}),{handlerDidError:async({request:e})=>self.fallback(e)}]}),"GET"),e.registerRoute(/^https:\/\/fonts\.(?:googleapis)\.com\/.*/i,new e.StaleWhileRevalidate({cacheName:"google-fonts-stylesheets",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:604800}),{handlerDidError:async({request:e})=>self.fallback(e)}]}),"GET"),e.registerRoute(/\.(?:eot|otf|ttc|ttf|woff|woff2|font.css)$/i,new e.StaleWhileRevalidate({cacheName:"static-font-assets",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:604800}),{handlerDidError:async({request:e})=>self.fallback(e)}]}),"GET"),e.registerRoute(/\.(?:jpg|jpeg|gif|png|svg|ico|webp)$/i,new e.StaleWhileRevalidate({cacheName:"static-image-assets",plugins:[new e.ExpirationPlugin({maxEntries:64,maxAgeSeconds:86400}),{handlerDidError:async({request:e})=>self.fallback(e)}]}),"GET"),e.registerRoute(/\/_next\/image\?url=.+$/i,new e.StaleWhileRevalidate({cacheName:"next-image",plugins:[new e.ExpirationPlugin({maxEntries:64,maxAgeSeconds:86400}),{handlerDidError:async({request:e})=>self.fallback(e)}]}),"GET"),e.registerRoute(/\.(?:mp3|wav|ogg)$/i,new e.CacheFirst({cacheName:"static-audio-assets",plugins:[new e.RangeRequestsPlugin,new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400}),{handlerDidError:async({request:e})=>self.fallback(e)}]}),"GET"),e.registerRoute(/\.(?:mp4)$/i,new e.CacheFirst({cacheName:"static-video-assets",plugins:[new e.RangeRequestsPlugin,new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400}),{handlerDidError:async({request:e})=>self.fallback(e)}]}),"GET"),e.registerRoute(/\.(?:js)$/i,new e.StaleWhileRevalidate({cacheName:"static-js-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400}),{handlerDidError:async({request:e})=>self.fallback(e)}]}),"GET"),e.registerRoute(/\.(?:css|less)$/i,new e.StaleWhileRevalidate({cacheName:"static-style-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400}),{handlerDidError:async({request:e})=>self.fallback(e)}]}),"GET"),e.registerRoute(/\/_next\/data\/.+\/.+\.json$/i,new e.StaleWhileRevalidate({cacheName:"next-data",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400}),{handlerDidError:async({request:e})=>self.fallback(e)}]}),"GET"),e.registerRoute(/\.(?:json|xml|csv)$/i,new e.NetworkFirst({cacheName:"static-data-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400}),{handlerDidError:async({request:e})=>self.fallback(e)}]}),"GET"),e.registerRoute(({url:e})=>{if(!(self.origin===e.origin))return!1;const s=e.pathname;return!s.startsWith("/api/auth/")&&!!s.startsWith("/api/")},new e.NetworkFirst({cacheName:"apis",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:16,maxAgeSeconds:86400}),{handlerDidError:async({request:e})=>self.fallback(e)}]}),"GET"),e.registerRoute(({url:e})=>{if(!(self.origin===e.origin))return!1;return!e.pathname.startsWith("/api/")},new e.NetworkFirst({cacheName:"others",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400}),{handlerDidError:async({request:e})=>self.fallback(e)}]}),"GET"),e.registerRoute(({url:e})=>!(self.origin===e.origin),new e.NetworkFirst({cacheName:"cross-origin",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:3600}),{handlerDidError:async({request:e})=>self.fallback(e)}]}),"GET")});
